// 系统监控相关的常量定义

/**
 * GPU内存使用率警告阈值 (%)
 * 当GPU内存使用率超过此值时，将触发警告
 */
export const GPU_MEMORY_THRESHOLD = 95

/**
 * GPU使用率警告阈值 (%)
 * 当GPU使用率超过此值时，将触发警告
 */
export const GPU_UTILIZATION_THRESHOLD = 95

/**
 * GPU温度警告阈值 (摄氏度)
 * 当GPU温度超过此值时，将触发警告
 */
export const GPU_TEMPERATURE_THRESHOLD = 80

/**
 * 功耗警告阈值 (%)
 * 当功耗超过此值时，将触发警告
 */
export const POWER_UTILIZATION_THRESHOLD = 90

/**
 * CPU使用率警告阈值 (%)
 * 当CPU使用率超过此值时，将触发警告
 */
export const CPU_UTILIZATION_THRESHOLD = 90

/**
 * 硬盘性能警告阈值 (%)
 * 当硬盘性能低于此值时，将触发警告
 */
export const DISK_PERFORMANCE_THRESHOLD = 20

/**
 * 内存使用率警告阈值 (%)
 * 当内存使用率超过此值时，将触发警告
 */
export const MEMORY_UTILIZATION_THRESHOLD = 90