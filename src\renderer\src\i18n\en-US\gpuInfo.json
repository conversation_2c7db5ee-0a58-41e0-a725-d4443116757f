{"title": "GPU Information", "loading": "Loading GPU information...", "error": "Failed to fetch GPU information", "retry": "Retry", "gpu": "GPU", "memory": "Memory", "gpuUtilization": "GPU Utilization", "cpuUtilization": "CPU Utilization", "utilization": "Utilization", "temperature": "Temperature", "fanSpeed": "Fan Speed", "power": "Power", "powerUtilization": "Power Utilization", "details": "Details", "pcie": "PCIe Link", "clock": "Core Clock", "memoryClock": "Memory Clock", "processes": "Processes", "refresh": "Refresh", "notSupported": "No NVIDIA GPU detected or NVIDIA drivers not installed", "diskUsage": "Disk Usage", "diskPerformance": "Disk Performance", "performance": "Performance", "highMemoryUsageWarning": "GPU memory usage has reached {threshold}%! May run out of memory at any time, consider upgrading your graphics card.", "highGpuUtilizationWarning": "GPU utilization has reached {threshold}%! GPU load is too high, consider reducing the load.", "highTemperatureWarning": "GPU temperature has reached {threshold}°C! Temperature is too high, consider improving cooling.", "highPowerUsageWarning": "Power usage has reached {threshold}%! Power consumption is too high, consider reducing the load.", "highCpuUsageWarning": "CPU utilization has reached {threshold}%! CPU load is too high, consider reducing the load.", "lowDiskPerformanceWarning": "Disk performance of {drive} has dropped to {threshold}%! Consider checking the disk health."}